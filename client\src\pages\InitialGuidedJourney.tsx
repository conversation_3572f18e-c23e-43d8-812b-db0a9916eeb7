import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Loader2, Upload } from "lucide-react";
import Header from "../components/Header";
import apiService from "../services/apiService";
import { guidedJourneyOrder } from "../data/SectionMetadata";
import { useGigOperations } from "../contexts/GigContext";
import FileUpload from "../components/FileUpload";
import { FileUploadState } from "../types/upload";

const InitialGuidedJourney: React.FC = () => {
  const [description, setDescription] = useState("");
  const [processLoading, setProcessLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [uploadState, setUploadState] = useState<FileUploadState>({
    isUploading: false,
    progress: null,
    error: null,
    success: false,
  });

  const navigate = useNavigate();

  // Use GigContext operations
  const { setActiveSection, generateSuggestionWithGig, setGig, reset } =
    useGigOperations();

  const handleSubmit = async () => {
    if (!description.trim()) {
      setError("Please enter a description");
      return;
    }

    setProcessLoading(true);
    setError(null);

    try {
      const gigData = await apiService.convertTextToGig(description);
      setGig(gigData);

      const firstSection = guidedJourneyOrder[0];
      setActiveSection(firstSection);
      navigate(`/guided-journey/${firstSection}`);
      generateSuggestionWithGig(firstSection, gigData);
    } catch (err) {
      setError("Failed to process the description. Please try again.");
      console.error(err);
    } finally {
      setProcessLoading(false);
    }
  };

  const handleFileUpload = () => {
    setShowFileUpload(true);
  };

  const handleFileSelect = (file: File) => {
    console.log("File selected:", file.name);
  };

  const handleUpload = async (file: File) => {
    setUploadState({
      isUploading: true,
      progress: { loaded: 0, total: file.size, percentage: 0 },
      error: null,
      success: false,
    });

    try {
      const response = await apiService.uploadAndConvertFile(
        file,
        (percentage) => {
          setUploadState((prev) => ({
            ...prev,
            progress: {
              loaded: (file.size * percentage) / 100,
              total: file.size,
              percentage,
            },
          }));
        }
      );

      if (response.success && response.gig) {
        setUploadState({
          isUploading: false,
          progress: null,
          error: null,
          success: true,
        });

        // Set the gig data and navigate to the first section
        setGig(response.gig);
        const firstSection = guidedJourneyOrder[0];
        setActiveSection(firstSection);
        navigate(`/guided-journey/${firstSection}`);
        generateSuggestionWithGig(firstSection, response.gig);
      } else {
        throw new Error(response.message || "Upload failed");
      }
    } catch (error) {
      setUploadState({
        isUploading: false,
        progress: null,
        error: error instanceof Error ? error.message : "Upload failed",
        success: false,
      });
    }
  };

  const handleCloseUpload = () => {
    setShowFileUpload(false);
    setUploadState({
      isUploading: false,
      progress: null,
      error: null,
      success: false,
    });
  };

  // Reset context when entering initial step
  React.useEffect(() => {
    reset();
  }, []);

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <div className="container mx-auto max-w-3xl py-16 px-4">
          <h1 className="text-3xl font-bold text-center mb-4">
            Describe Your Gig
          </h1>
          <p className="text-gray-600 text-center mb-8">
            Start by providing a rough description of your gig. We'll help
            organize it into sections later.
          </p>
          <div className="flex flex-col gap-4">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="relative mb-4">
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe your gig in plain English — The outcomes you want, activities that you will want a provider to do, and such. Or upload a file if you already have something written. We'll help turn your rough notes into a polished job description."
                  className="w-full h-64 p-4 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={processLoading}
                />
                {processLoading && (
                  <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
                    <Loader2 className="animate-spin text-blue-600" />
                  </div>
                )}
              </div>

              {error && <p className="text-red-500 mb-4">{error}</p>}

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={handleSubmit}
                  disabled={processLoading || !description.trim()}
                  className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors text-lg font-medium"
                >
                  {processLoading ? (
                    <span className="flex items-center justify-center gap-2">
                      <Loader2 className="animate-spin" size={20} />
                      Processing...
                    </span>
                  ) : (
                    "Continue"
                  )}
                </button>
                <button
                  onClick={handleFileUpload}
                  disabled={processLoading}
                  className="flex items-center justify-center gap-2 border border-gray-300 py-3 px-6 rounded-lg hover:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  <Upload size={20} />
                  Upload a file
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* File Upload Modal */}
        {showFileUpload && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Upload Your File</h2>
                  <button
                    onClick={handleCloseUpload}
                    className="text-gray-500 hover:text-gray-700 text-2xl"
                    disabled={uploadState.isUploading}
                  >
                    ×
                  </button>
                </div>

                <FileUpload
                  onFileSelect={handleFileSelect}
                  onUpload={handleUpload}
                  uploadState={uploadState}
                  disabled={uploadState.isUploading}
                  showProgress={true}
                />

                {uploadState.success && (
                  <div className="mt-4 text-center">
                    <p className="text-green-600 mb-2">
                      File uploaded successfully! Starting guided journey...
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default InitialGuidedJourney;
