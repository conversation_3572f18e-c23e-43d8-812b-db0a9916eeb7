import React, { useState } from 'react';
import { ExportFormat, ExportState, EXPORT_FORMATS } from '../types/export';
import { Gig } from '../types/gig';

interface ExportOptionsProps {
  gigData: Gig;
  onExport: (format: ExportFormat['type']) => Promise<void>;
  exportState: ExportState;
  className?: string;
}

const ExportOptions: React.FC<ExportOptionsProps> = ({
  gigData,
  onExport,
  exportState,
  className = ''
}) => {
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat['type']>('pdf');
  const [showOptions, setShowOptions] = useState(false);

  const handleExport = async () => {
    if (!exportState.isExporting) {
      await onExport(selectedFormat);
    }
  };

  const hasContent = gigData && (gigData.title || gigData.summary);

  if (!hasContent) {
    return (
      <div className={`export-container ${className}`}>
        <div className="export-disabled">
          <div className="disabled-icon">📄</div>
          <p className="disabled-text">
            Create your gig content first to enable export options
          </p>
        </div>

        <style jsx>{`
          .export-container {
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #f9fafb;
          }

          .export-disabled {
            text-align: center;
            color: #6b7280;
          }

          .disabled-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
          }

          .disabled-text {
            margin: 0;
            font-size: 0.875rem;
          }
        `}</style>
      </div>
    );
  }

  return (
    <div className={`export-container ${className}`}>
      <div className="export-header">
        <h3 className="export-title">Export Your Gig</h3>
        <p className="export-subtitle">
          Download your gig description in your preferred format
        </p>
      </div>

      <div className="export-content">
        {/* Format Selection */}
        <div className="format-selection">
          <label className="format-label">Choose Export Format:</label>
          <div className="format-options">
            {EXPORT_FORMATS.map((format) => (
              <label key={format.type} className="format-option">
                <input
                  type="radio"
                  name="exportFormat"
                  value={format.type}
                  checked={selectedFormat === format.type}
                  onChange={(e) => setSelectedFormat(e.target.value as ExportFormat['type'])}
                  disabled={exportState.isExporting}
                />
                <div className="format-info">
                  <div className="format-name">{format.name}</div>
                  <div className="format-description">{format.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Export Actions */}
        <div className="export-actions">
          <button
            type="button"
            className="export-button primary"
            onClick={handleExport}
            disabled={exportState.isExporting}
          >
            {exportState.isExporting ? (
              <>
                <span className="loading-spinner">⏳</span>
                Exporting...
              </>
            ) : (
              <>
                <span className="export-icon">📥</span>
                Export as {EXPORT_FORMATS.find(f => f.type === selectedFormat)?.name}
              </>
            )}
          </button>

          <button
            type="button"
            className="preview-button"
            onClick={() => setShowOptions(!showOptions)}
            disabled={exportState.isExporting}
          >
            {showOptions ? 'Hide' : 'Show'} Preview
          </button>
        </div>

        {/* Export Status */}
        {exportState.success && (
          <div className="export-success">
            <div className="success-icon">✅</div>
            <div className="success-text">
              Export completed successfully! Your download should start automatically.
            </div>
          </div>
        )}

        {exportState.error && (
          <div className="export-error">
            <div className="error-icon">❌</div>
            <div className="error-text">{exportState.error}</div>
          </div>
        )}

        {/* Content Preview */}
        {showOptions && (
          <div className="content-preview">
            <h4 className="preview-title">Content Preview</h4>
            <div className="preview-sections">
              {gigData.title && (
                <div className="preview-section">
                  <strong>Title:</strong> {gigData.title}
                </div>
              )}
              {gigData.summary && (
                <div className="preview-section">
                  <strong>Summary:</strong> {gigData.summary.substring(0, 100)}
                  {gigData.summary.length > 100 ? '...' : ''}
                </div>
              )}
              {gigData.deliverables && (
                <div className="preview-section">
                  <strong>Deliverables:</strong> {gigData.deliverables.substring(0, 100)}
                  {gigData.deliverables.length > 100 ? '...' : ''}
                </div>
              )}
              {gigData.skills && (
                <div className="preview-section">
                  <strong>Skills:</strong> {gigData.skills.substring(0, 100)}
                  {gigData.skills.length > 100 ? '...' : ''}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .export-container {
          padding: 1.5rem;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          background-color: white;
        }

        .export-header {
          margin-bottom: 1.5rem;
        }

        .export-title {
          margin: 0 0 0.5rem 0;
          font-size: 1.25rem;
          font-weight: 600;
          color: #111827;
        }

        .export-subtitle {
          margin: 0;
          font-size: 0.875rem;
          color: #6b7280;
        }

        .export-content {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .format-selection {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .format-label {
          font-weight: 500;
          color: #374151;
        }

        .format-options {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .format-option {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .format-option:hover {
          border-color: #3b82f6;
          background-color: #f8fafc;
        }

        .format-option input[type="radio"] {
          margin: 0;
        }

        .format-info {
          flex: 1;
        }

        .format-name {
          font-weight: 500;
          color: #374151;
        }

        .format-description {
          font-size: 0.875rem;
          color: #6b7280;
        }

        .export-actions {
          display: flex;
          gap: 0.75rem;
          flex-wrap: wrap;
        }

        .export-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background-color: #3b82f6;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 500;
          transition: background-color 0.2s ease;
        }

        .export-button:hover:not(:disabled) {
          background-color: #1d4ed8;
        }

        .export-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .preview-button {
          background-color: #f3f4f6;
          color: #374151;
          border: 1px solid #d1d5db;
          padding: 0.75rem 1rem;
          border-radius: 6px;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .preview-button:hover:not(:disabled) {
          background-color: #e5e7eb;
        }

        .loading-spinner {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        .export-success, .export-error {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem;
          border-radius: 6px;
        }

        .export-success {
          background-color: #d1fae5;
          color: #065f46;
          border: 1px solid #a7f3d0;
        }

        .export-error {
          background-color: #fee2e2;
          color: #991b1b;
          border: 1px solid #fca5a5;
        }

        .content-preview {
          border-top: 1px solid #e5e7eb;
          padding-top: 1.5rem;
        }

        .preview-title {
          margin: 0 0 1rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: #374151;
        }

        .preview-sections {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .preview-section {
          padding: 0.5rem;
          background-color: #f9fafb;
          border-radius: 4px;
          font-size: 0.875rem;
          line-height: 1.4;
        }

        .preview-section strong {
          color: #374151;
        }
      `}</style>
    </div>
  );
};

export default ExportOptions;
