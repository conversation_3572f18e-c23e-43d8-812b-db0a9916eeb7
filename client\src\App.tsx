import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Home from "./pages/Home";
import Content from "./pages/Content";
import InitialGuidedJourney from "./pages/InitialGuidedJourney";
import SectionGuidedJourney from "./pages/SectionGuidedJourney";
import Login from "./pages/Login";
import { GigProvider } from "./contexts/GigContext";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";

function App() {
  return (
    <AuthProvider>
      <GigProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Home />
                </ProtectedRoute>
              }
            />
            <Route
              path="/content"
              element={
                <ProtectedRoute>
                  <Content />
                </ProtectedRoute>
              }
            />
            <Route
              path="/guided-journey"
              element={
                <ProtectedRoute>
                  <InitialGuidedJourney />
                </ProtectedRoute>
              }
            />
            <Route
              path="/guided-journey/:sectionId"
              element={
                <ProtectedRoute>
                  <SectionGuidedJourney />
                </ProtectedRoute>
              }
            />
          </Routes>
        </Router>
      </GigProvider>
    </AuthProvider>
  );
}

export default App;
