import express from 'express';
import { uploadSingle, handleUploadError } from '../middleware/upload.js';
import { extractTextFromFile, sanitizeExtractedText, getFileInfo } from '../services/fileProcessor.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { requireAuth } from '../middleware/auth.js';

const router = express.Router();

/**
 * Upload and process file endpoint
 * Accepts file upload, extracts text, and returns the content
 */
router.post('/upload-file', 
  requireAuth,
  uploadSingle,
  handleUploadError,
  asyncHandler(async (req, res) => {
    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please select a file to upload'
      });
    }

    const { path: filePath, originalname } = req.file;
    
    try {
      // Log file info for debugging
      const fileInfo = getFileInfo(filePath, originalname);
      console.log('Processing uploaded file:', fileInfo);

      // Extract text from the uploaded file
      const extractedText = await extractTextFromFile(filePath, originalname);
      
      // Sanitize the extracted text
      const cleanText = sanitizeExtractedText(extractedText);
      
      if (!cleanText || cleanText.length < 10) {
        return res.status(400).json({
          error: 'Insufficient content',
          message: 'The uploaded file does not contain enough readable text content'
        });
      }

      // Return the extracted text
      res.json({
        success: true,
        text: cleanText,
        fileInfo: {
          originalName: originalname,
          size: req.file.size,
          type: req.file.mimetype
        },
        message: 'File processed successfully'
      });

    } catch (error) {
      console.error('File processing error:', error);
      
      // Return appropriate error message
      if (error.message.includes('No text content could be extracted')) {
        return res.status(400).json({
          error: 'No content found',
          message: 'The uploaded file appears to be empty or contains no readable text'
        });
      }
      
      if (error.message.includes('Unsupported file type')) {
        return res.status(400).json({
          error: 'Unsupported file type',
          message: 'Please upload a .txt, .pdf, .doc, or .docx file'
        });
      }
      
      return res.status(500).json({
        error: 'File processing failed',
        message: 'An error occurred while processing your file. Please try again.'
      });
    }
  })
);

/**
 * Upload file and convert to gig endpoint
 * Combines file upload with gig conversion in one step
 */
router.post('/upload-and-convert',
  requireAuth,
  uploadSingle,
  handleUploadError,
  asyncHandler(async (req, res) => {
    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please select a file to upload'
      });
    }

    const { path: filePath, originalname } = req.file;
    
    try {
      // Extract text from the uploaded file
      const extractedText = await extractTextFromFile(filePath, originalname);
      const cleanText = sanitizeExtractedText(extractedText);
      
      if (!cleanText || cleanText.length < 10) {
        return res.status(400).json({
          error: 'Insufficient content',
          message: 'The uploaded file does not contain enough readable text content'
        });
      }

      // Import the convert function (avoiding circular imports)
      const { convertTextToGig } = await import('./convertRoute.js');
      
      // Create a mock request object for the convert function
      const mockReq = {
        body: { text: cleanText },
        session: req.session
      };
      
      // Create a mock response object to capture the result
      let convertResult = null;
      const mockRes = {
        json: (data) => {
          convertResult = data;
        }
      };
      
      // Call the convert function
      await convertTextToGig(mockReq, mockRes);
      
      if (!convertResult) {
        throw new Error('Failed to convert text to gig format');
      }
      
      // Return the converted gig data along with file info
      res.json({
        success: true,
        gig: convertResult,
        fileInfo: {
          originalName: originalname,
          size: req.file.size,
          type: req.file.mimetype
        },
        message: 'File uploaded and converted successfully'
      });

    } catch (error) {
      console.error('File upload and conversion error:', error);
      
      return res.status(500).json({
        error: 'Processing failed',
        message: 'An error occurred while processing your file. Please try again.'
      });
    }
  })
);

export default router;
