export interface FileUploadResponse {
  success: boolean;
  text?: string;
  fileInfo?: {
    originalName: string;
    size: number;
    type: string;
  };
  message: string;
  error?: string;
}

export interface FileUploadAndConvertResponse {
  success: boolean;
  gig?: any; // Using any for now, should match Gig type
  fileInfo?: {
    originalName: string;
    size: number;
    type: string;
  };
  message: string;
  error?: string;
}

export interface FileUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface FileUploadState {
  isUploading: boolean;
  progress: FileUploadProgress | null;
  error: string | null;
  success: boolean;
}

export const SUPPORTED_FILE_TYPES = [
  "text/plain",
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

export const SUPPORTED_FILE_EXTENSIONS = [".txt", ".pdf", ".doc", ".docx"];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
