import express from "express";
import { improveRoute } from "./improveRoute.js";
import { convertTextToGig } from "./convertRoute.js";
import {
  validateImproveRequest,
  validateConvertRequest,
} from "../middleware/validation.js";
import { requireAuth } from "../middleware/auth.js";
import uploadRoutes from "./uploadRoute.js";
import exportRoutes from "./exportRoute.js";

const router = express.Router();

router.post("/improve", requireAuth, validateImproveRequest, improveRoute);
router.post(
  "/convert-text-to-gig",
  requireAuth,
  validateConvertRequest,
  convertTextToGig
);

// File upload routes
router.use("/upload", uploadRoutes);

// Export routes
router.use("/export", exportRoutes);

export default router;
