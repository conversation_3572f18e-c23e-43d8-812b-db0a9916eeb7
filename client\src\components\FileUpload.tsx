import React, { useState, useRef } from "react";
import {
  FileUploadState,
  SUPPORTED_FILE_EXTENSIONS,
  MAX_FILE_SIZE,
} from "../types/upload";

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onUpload: (file: File) => Promise<void>;
  uploadState: FileUploadState;
  disabled?: boolean;
  className?: string;
  showProgress?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  onUpload,
  uploadState,
  disabled = false,
  className = "",
  showProgress = true,
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return `File size must be less than ${Math.round(
        MAX_FILE_SIZE / (1024 * 1024)
      )}MB`;
    }

    // Check file extension
    const extension = "." + file.name.split(".").pop()?.toLowerCase();
    if (!SUPPORTED_FILE_EXTENSIONS.includes(extension)) {
      return `File type not supported. Please upload: ${SUPPORTED_FILE_EXTENSIONS.join(
        ", "
      )}`;
    }

    return null;
  };

  const handleFileSelect = (file: File) => {
    const error = validateFile(file);
    if (error) {
      alert(error);
      return;
    }

    setSelectedFile(file);
    onFileSelect(file);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled || uploadState.isUploading) return;

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleUploadClick = async () => {
    if (selectedFile && !uploadState.isUploading) {
      await onUpload(selectedFile);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className={`file-upload-container ${className}`}>
      <div
        className={`file-upload-area ${dragActive ? "drag-active" : ""} ${
          disabled || uploadState.isUploading ? "disabled" : ""
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={SUPPORTED_FILE_EXTENSIONS.join(",")}
          onChange={handleInputChange}
          style={{ display: "none" }}
          disabled={disabled || uploadState.isUploading}
        />

        {!selectedFile ? (
          <div className="upload-prompt">
            <div className="upload-icon">📁</div>
            <p className="upload-text">
              Drag and drop your file here, or{" "}
              <button
                type="button"
                className="browse-button"
                onClick={handleBrowseClick}
                disabled={disabled || uploadState.isUploading}
              >
                browse
              </button>
            </p>
            <p className="upload-hint">
              Supported formats: {SUPPORTED_FILE_EXTENSIONS.join(", ")}
              <br />
              Maximum size: {Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB
            </p>
          </div>
        ) : (
          <div className="file-selected">
            <div className="file-info">
              <div className="file-icon">📄</div>
              <div className="file-details">
                <div className="file-name">{selectedFile.name}</div>
                <div className="file-size">
                  {formatFileSize(selectedFile.size)}
                </div>
              </div>
            </div>

            {!uploadState.isUploading && !uploadState.success && (
              <div className="upload-actions">
                <button
                  type="button"
                  className="upload-button primary"
                  onClick={handleUploadClick}
                  disabled={disabled}
                >
                  Upload File
                </button>
                <button
                  type="button"
                  className="change-file-button"
                  onClick={handleBrowseClick}
                  disabled={disabled}
                >
                  Change File
                </button>
              </div>
            )}
          </div>
        )}

        {/* Upload Progress */}
        {showProgress && uploadState.isUploading && (
          <div className="upload-progress">
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{
                  width: uploadState.progress
                    ? `${uploadState.progress.percentage}%`
                    : "0%",
                }}
              />
            </div>
            <div className="progress-text">
              {uploadState.progress
                ? `Uploading... ${uploadState.progress.percentage}%`
                : "Processing..."}
            </div>
          </div>
        )}

        {/* Success Message */}
        {uploadState.success && (
          <div className="upload-success">
            <div className="success-icon">✅</div>
            <div className="success-text">File uploaded successfully!</div>
          </div>
        )}

        {/* Error Message */}
        {uploadState.error && (
          <div className="upload-error">
            <div className="error-icon">❌</div>
            <div className="error-text">{uploadState.error}</div>
          </div>
        )}
      </div>

      <style>{`
        .file-upload-container {
          width: 100%;
        }

        .file-upload-area {
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          padding: 2rem;
          text-align: center;
          transition: all 0.2s ease;
          background-color: #f9fafb;
        }

        .file-upload-area:hover:not(.disabled) {
          border-color: #3b82f6;
          background-color: #eff6ff;
        }

        .file-upload-area.drag-active {
          border-color: #3b82f6;
          background-color: #eff6ff;
        }

        .file-upload-area.disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .upload-prompt {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
        }

        .upload-icon {
          font-size: 3rem;
        }

        .upload-text {
          font-size: 1.1rem;
          color: #374151;
          margin: 0;
        }

        .browse-button {
          color: #3b82f6;
          text-decoration: underline;
          background: none;
          border: none;
          cursor: pointer;
          font-size: inherit;
        }

        .browse-button:hover:not(:disabled) {
          color: #1d4ed8;
        }

        .upload-hint {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0;
        }

        .file-selected {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .file-info {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem;
          background-color: white;
          border-radius: 6px;
          border: 1px solid #e5e7eb;
        }

        .file-icon {
          font-size: 2rem;
        }

        .file-details {
          flex: 1;
          text-align: left;
        }

        .file-name {
          font-weight: 500;
          color: #374151;
        }

        .file-size {
          font-size: 0.875rem;
          color: #6b7280;
        }

        .upload-actions {
          display: flex;
          gap: 0.5rem;
          justify-content: center;
        }

        .upload-button {
          background-color: #3b82f6;
          color: white;
          border: none;
          padding: 0.5rem 1rem;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 500;
        }

        .upload-button:hover:not(:disabled) {
          background-color: #1d4ed8;
        }

        .change-file-button {
          background-color: #f3f4f6;
          color: #374151;
          border: 1px solid #d1d5db;
          padding: 0.5rem 1rem;
          border-radius: 6px;
          cursor: pointer;
        }

        .change-file-button:hover:not(:disabled) {
          background-color: #e5e7eb;
        }

        .upload-progress {
          margin-top: 1rem;
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background-color: #e5e7eb;
          border-radius: 4px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background-color: #3b82f6;
          transition: width 0.3s ease;
        }

        .progress-text {
          margin-top: 0.5rem;
          font-size: 0.875rem;
          color: #6b7280;
        }

        .upload-success, .upload-error {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          justify-content: center;
          margin-top: 1rem;
          padding: 0.75rem;
          border-radius: 6px;
        }

        .upload-success {
          background-color: #d1fae5;
          color: #065f46;
        }

        .upload-error {
          background-color: #fee2e2;
          color: #991b1b;
        }

        .success-icon, .error-icon {
          font-size: 1.25rem;
        }
      `}</style>
    </div>
  );
};

export default FileUpload;
