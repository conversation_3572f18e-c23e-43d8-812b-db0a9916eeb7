import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ClipboardList, Edit2, Upload } from "lucide-react";
import Header from "../components/Header";
import FileUpload from "../components/FileUpload";
import { FileUploadState } from "../types/upload";
import apiService from "../services/apiService";

const Home: React.FC = () => {
  const navigate = useNavigate();
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [uploadState, setUploadState] = useState<FileUploadState>({
    isUploading: false,
    progress: null,
    error: null,
    success: false,
  });

  const handleGuidedPath = () => {
    navigate("/guided-journey");
  };

  const handleDirectPath = () => {
    navigate("/content");
  };

  const handleFileUpload = () => {
    setShowFileUpload(true);
  };

  const handleFileSelect = (file: File) => {
    console.log("File selected:", file.name);
  };

  const handleUpload = async (file: File) => {
    setUploadState({
      isUploading: true,
      progress: { loaded: 0, total: file.size, percentage: 0 },
      error: null,
      success: false,
    });

    try {
      const response = await apiService.uploadAndConvertFile(
        file,
        (percentage) => {
          setUploadState((prev) => ({
            ...prev,
            progress: {
              loaded: (file.size * percentage) / 100,
              total: file.size,
              percentage,
            },
          }));
        }
      );

      if (response.success && response.gig) {
        setUploadState({
          isUploading: false,
          progress: null,
          error: null,
          success: true,
        });

        // Navigate to content page with the uploaded gig data
        navigate("/content", { state: { gigData: response.gig } });
      } else {
        throw new Error(response.message || "Upload failed");
      }
    } catch (error) {
      setUploadState({
        isUploading: false,
        progress: null,
        error: error instanceof Error ? error.message : "Upload failed",
        success: false,
      });
    }
  };

  const handleCloseUpload = () => {
    setShowFileUpload(false);
    setUploadState({
      isUploading: false,
      progress: null,
      error: null,
      success: false,
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-center mb-8">
          Create Your Gig Description
        </h1>
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Guided Journey Card */}
          <div
            className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer"
            onClick={handleGuidedPath}
          >
            <div className="flex flex-col items-center text-center">
              <ClipboardList className="w-16 h-16 text-blue-600 mb-4" />
              <h2 className="text-xl font-semibold mb-3">Guided Journey</h2>
              <p className="text-gray-600 mb-4">
                Let us help you create the perfect gig description with our
                step-by-step guidance.
              </p>
              <button className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors">
                Start Guided Journey
              </button>
            </div>
          </div>

          {/* Direct Editor Card */}
          <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="flex flex-col items-center text-center">
              <Edit2 className="w-16 h-16 text-green-600 mb-4" />
              <h2 className="text-xl font-semibold mb-3">Direct Editor</h2>
              <p className="text-gray-600 mb-4">
                Jump straight into creating your gig description or upload an
                existing one.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 w-full">
                <button
                  onClick={handleDirectPath}
                  className="flex-1 bg-green-600 text-white px-6 py-2 rounded-full hover:bg-green-700 transition-colors"
                >
                  Go to Editor
                </button>
                <button
                  onClick={handleFileUpload}
                  className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-2 rounded-full transition-colors flex items-center justify-center gap-2"
                >
                  <Upload size={20} />
                  Upload File
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                Supported files: .txt, .pdf, .doc, .docx
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* File Upload Modal */}
      {showFileUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Upload Your File</h2>
                <button
                  onClick={handleCloseUpload}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                  disabled={uploadState.isUploading}
                >
                  ×
                </button>
              </div>

              <FileUpload
                onFileSelect={handleFileSelect}
                onUpload={handleUpload}
                uploadState={uploadState}
                disabled={uploadState.isUploading}
                showProgress={true}
              />

              {uploadState.success && (
                <div className="mt-4 text-center">
                  <p className="text-green-600 mb-2">
                    File uploaded successfully! Redirecting to editor...
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Home;
